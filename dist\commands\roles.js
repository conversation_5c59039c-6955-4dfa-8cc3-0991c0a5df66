"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('roles')
        .setDescription('Display all role achievements available to unlock'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const roles = await User_1.RoleForSale.find().sort({ price: 1 }); // Sort by price ascending
            if (!roles.length) {
                const embed = (0, embedBuilder_1.createEconomyEmbed)('Role Achievements')
                    .setDescription(`${embedBuilder_1.EMOJIS.ROLES.MASK} No role achievements are currently available to unlock!\n\nCheck back later or ask an administrator to add some role achievements.`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            // Create rich embed with role information
            const embed = (0, embedBuilder_1.createEconomyEmbed)('Phalanx Role Achievements')
                .setDescription(`${embedBuilder_1.EMOJIS.ROLES.SHIELD} **Available Role Achievements**\n\nUse \`/buyrole\` to unlock any of these exclusive role achievements! Your PLC balance is never spent - it represents your lifetime achievements.`)
                .setThumbnail('https://cdn.discordapp.com/emojis/1234567890123456789.png'); // You can add a custom shop icon
            // Add each role as a field
            roles.forEach((role, index) => {
                const roleEmoji = [embedBuilder_1.EMOJIS.ROLES.MEDAL, embedBuilder_1.EMOJIS.ROLES.SHIELD, embedBuilder_1.EMOJIS.ROLES.ARMOR, embedBuilder_1.EMOJIS.ROLES.BADGE][index % 4];
                embed.addFields({
                    name: `${roleEmoji} ${role.name}`,
                    value: `${(0, embedBuilder_1.formatCoins)(role.price)}\n${role.description || '*No description available*'}`,
                    inline: true
                });
            });
            // Add summary information
            const totalRoles = roles.length;
            const cheapestRole = Math.min(...roles.map(r => r.price));
            const mostExpensiveRole = Math.max(...roles.map(r => r.price));
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Achievement Summary`,
                value: `**${totalRoles}** achievements available\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Lowest Requirement: **${cheapestRole}** coins\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.DIAMOND} Highest Requirement: **${mostExpensiveRole}** coins`,
                inline: false
            });
            embed.setFooter({
                text: 'Use /buyrole [role name] to unlock a role achievement!'
            });
            // Create role purchase buttons for the first few roles
            const roleButtons = new discord_js_1.ActionRowBuilder();
            const topRoles = roles.slice(0, 3); // Show buttons for first 3 roles
            topRoles.forEach(role => {
                roleButtons.addComponents(new discord_js_1.ButtonBuilder()
                    .setCustomId(`buy_role_${role.roleId}`)
                    .setLabel(`Unlock ${role.name}`)
                    .setEmoji(embedBuilder_1.EMOJIS.ECONOMY.COINS)
                    .setStyle(discord_js_1.ButtonStyle.Primary));
            });
            // Create quick action buttons
            const actionButtons = (0, embedBuilder_1.createQuickActionButtons)();
            const components = [actionButtons];
            if (topRoles.length > 0) {
                components.unshift(roleButtons);
            }
            await interaction.reply({
                embeds: [embed],
                components: components,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to fetch roles.');
            }
        }
    })
};
