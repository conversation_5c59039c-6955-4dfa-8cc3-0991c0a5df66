import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { RoleForSale } from '../models/User';
import { withErrorHandler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('editrole')
        .setDescription('Edit a role achievement (admin only)')
        .addStringOption(option => option.setName('role').setDescription('Role name or ID').setRequired(true))
        .addStringOption(option => option.setName('name').setDescription('New display name for the achievement').setRequired(false))
        .addIntegerOption(option => option.setName('price').setDescription('New required PLC balance to unlock').setRequired(false))
        .addStringOption(option => option.setName('description').setDescription('New achievement description').setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const roleInput = interaction.options.getString('role', true);
        const newName = interaction.options.getString('name');
        const newPrice = interaction.options.getInteger('price');
        const newDescription = interaction.options.getString('description');

        // Validation
        if (newPrice !== null && newPrice <= 0) {
            throw new ValidationError('Price must be greater than zero.');
        }

        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        try {
            // Resolve the role by name or ID
            const roleResolution = await resolveRole(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;

            // Validate role permissions
            validateRolePermissions(interaction.guild, role);

            // Build update object
            const update: Record<string, any> = {};
            if (newName) update.name = newName;
            if (newPrice !== null) update.price = newPrice;
            if (newDescription !== null) update.description = newDescription;

            // Check if there are any updates
            if (Object.keys(update).length === 0) {
                throw new ValidationError('No changes specified. Please provide at least one field to update.');
            }

            // Update role in database
            const updated = await RoleForSale.findOneAndUpdate(
                { roleId: role.id },
                update,
                { new: true }
            );

            if (!updated) {
                throw new ValidationError(`Role achievement "${role.name}" not found.`);
            }

            // Create success message with resolution info
            let successMessage = `Role achievement **${updated.name}** updated successfully! ✏️`;

            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
            }

            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        } catch (error: unknown) {
            if (error instanceof ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError(error.message);
            } else {
                throw new DatabaseError('Failed to update role achievement.');
            }
        }
    })
};
