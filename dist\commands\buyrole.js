"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const economyService_1 = require("../services/economyService");
const User_2 = __importDefault(require("../models/User"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('buyrole')
        .setDescription('Unlock a role achievement with your accumulated PLC')
        .addStringOption(option => option.setName('role')
        .setDescription('The name of the role achievement to unlock (case-sensitive)')
        .setRequired(true)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const guild = interaction.guild;
        if (!guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        const roleName = interaction.options.getString('role', true);
        const discordId = interaction.user.id;
        // Find the role achievement
        const roleForSale = await User_1.RoleForSale.findOne({ name: roleName });
        if (!roleForSale) {
            throw new errorHandler_1.ValidationError(`Role achievement "${roleName}" is not available. Use \`/roles\` to see available achievements.`);
        }
        // Find the Discord role object
        const discordRole = guild.roles.cache.get(roleForSale.roleId);
        if (!discordRole) {
            throw new errorHandler_1.ValidationError('The role achievement exists but the role is not on this server. Contact an admin.');
        }
        // Get member and check existing role
        const member = await guild.members.fetch(discordId);
        if (!member) {
            throw new errorHandler_1.ValidationError('Could not find your member profile in this server.');
        }
        if (member.roles.cache.has(discordRole.id)) {
            throw new errorHandler_1.ValidationError('You have already unlocked this role achievement.');
        }
        // Check user balance before attempting achievement unlock
        const user = await User_2.default.findOne({ discordId });
        const currentBalance = user?.balance || 0;
        if (currentBalance < roleForSale.price) {
            throw new errorHandler_1.BalanceError(`You need ${roleForSale.price} coins to unlock this achievement but only have ${currentBalance} coins. You need ${roleForSale.price - currentBalance} more coins to unlock this role.`);
        }
        try {
            // First assign the role
            await member.roles.add(discordRole);
            // Record the achievement unlock (no balance deduction)
            await (0, economyService_1.adjustBalance)(discordId, 0, 'role_achievement', `Achievement unlocked: ${roleForSale.name} (Required: ${roleForSale.price} PLC)`);
            // Create rich success embed for achievement unlock
            const embed = (0, embedBuilder_1.createSuccessEmbed)('Achievement Unlocked!')
                .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Congratulations!**\n\n` +
                `You have unlocked the **${roleForSale.name}** role achievement!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Achievement Unlocked`,
                value: `**${roleForSale.name}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Required Balance`,
                value: (0, embedBuilder_1.formatCoins)(roleForSale.price),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.SUCCESS.CROWN} New Status`,
                value: `Role has been added to your profile!`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Achievement Details`,
                value: roleForSale.description || 'Congratulations on your achievement!',
                inline: false
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Achievement Time`,
                value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: false
            })
                .setFooter({
                text: 'Your PLC balance remains unchanged - achievements are permanent!'
            });
            // Add user's avatar to embed
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
        }
        catch (error) {
            // If we get here and the role was added, we need to remove it
            if (member.roles.cache.has(discordRole.id)) {
                try {
                    await member.roles.remove(discordRole);
                }
                catch (removeError) {
                    console.error('Failed to remove role after transaction failure:', removeError);
                }
            }
            if (error instanceof errorHandler_1.BalanceError) {
                throw error;
            }
            else if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError('Failed to process role achievement. Please try again.', error);
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while processing your achievement.');
        }
    })
};
