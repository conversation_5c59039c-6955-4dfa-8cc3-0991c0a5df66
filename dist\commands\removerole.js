"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('removerole')
        .setDescription('Remove a role achievement (admin only)')
        .addStringOption(option => option.setName('role').setDescription('Role name or ID').setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const roleInput = interaction.options.getString('role', true);
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        try {
            // Resolve the role by name or ID
            const roleResolution = await (0, roleResolver_1.resolveRole)(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;
            // Validate role permissions
            (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            // Find the role achievement
            const shopRole = await User_1.RoleForSale.findOne({ roleId: role.id });
            if (!shopRole) {
                throw new errorHandler_1.ValidationError(`Role achievement "${role.name}" does not exist.`);
            }
            const roleName = shopRole.name; // Store name before deletion
            await User_1.RoleForSale.findOneAndDelete({ roleId: role.id });
            // Create success message with resolution info
            let successMessage = `Role achievement **${roleName}** has been removed! 🗑️`;
            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
            }
            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to remove role achievement.');
            }
        }
    })
};
