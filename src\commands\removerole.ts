import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { RoleForSale } from '../models/User';
import { withError<PERSON>andler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('removerole')
        .setDescription('Remove a role achievement (admin only)')
        .addStringOption(option => option.setName('role').setDescription('Role name or ID').setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const roleInput = interaction.options.getString('role', true);

        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        try {
            // Resolve the role by name or ID
            const roleResolution = await resolveRole(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;

            // Validate role permissions
            validateRolePermissions(interaction.guild, role);

            // Find the role achievement
            const shopRole = await RoleForSale.findOne({ roleId: role.id });
            if (!shopRole) {
                throw new ValidationError(`Role achievement "${role.name}" does not exist.`);
            }

            const roleName = shopRole.name; // Store name before deletion
            await RoleForSale.findOneAndDelete({ roleId: role.id });

            // Create success message with resolution info
            let successMessage = `Role achievement **${roleName}** has been removed! 🗑️`;

            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
            }

            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        } catch (error: unknown) {
            if (error instanceof ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError(error.message);
            } else {
                throw new DatabaseError('Failed to remove role achievement.');
            }
        }
    })
};
