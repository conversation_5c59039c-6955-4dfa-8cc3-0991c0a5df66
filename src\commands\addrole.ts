import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { RoleForSale } from '../models/User';
import { withErrorHandler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('addrole')
        .setDescription('Add a role achievement (admin only)')
        .addStringOption(option => option.setName('role').setDescription('Role name or ID').setRequired(true))
        .addIntegerOption(option => option.setName('price').setDescription('Required PLC balance to unlock').setRequired(true))
        .addStringOption(option => option.setName('description').setDescription('Achievement description').setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    execute: with<PERSON>rror<PERSON><PERSON><PERSON>(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const roleInput = interaction.options.getString('role', true);
        const price = interaction.options.getInteger('price', true);
        const description = interaction.options.getString('description') || '';

        // Validation
        if (price <= 0) {
            throw new ValidationError('Price must be greater than zero.');
        }

        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        try {
            // Resolve the role by name or ID
            const roleResolution = await resolveRole(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;

            // Validate role permissions
            validateRolePermissions(interaction.guild, role);

            // Check if role achievement already exists
            const exists = await RoleForSale.findOne({ roleId: role.id });
            if (exists) {
                throw new ValidationError(`Role achievement "${role.name}" already exists.`);
            }

            // Add role achievement
            await RoleForSale.create({
                roleId: role.id,
                name: role.name,
                price,
                description
            });

            // Create success message with resolution info
            let successMessage = `Role achievement **${role.name}** added with requirement of **${price}** Phalanx Loyalty Coins! 🏆`;

            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                successMessage += `\n*Note: Resolved "${roleInput}" to "${role.name}"*`;
            }

            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        } catch (error: unknown) {
            if (error instanceof ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError(error.message);
            } else {
                throw new DatabaseError('Failed to add role achievement.');
            }
        }
    })
};
